import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { BrowserRouter, Routes, Route } from "react-router";
import { SocketProvider } from "./context/SocketProvider.jsx";
import RoomPage from "./pages/RoomPage.jsx";

createRoot(document.getElementById("root")).render(
  <BrowserRouter>
    <SocketProvider>
      <Routes>
        <Route path='/' element={<App />} />
        <Route path='/room/:roomId' element={<RoomPage />} />
      </Routes>
    </SocketProvider>
  </BrowserRouter>
);
