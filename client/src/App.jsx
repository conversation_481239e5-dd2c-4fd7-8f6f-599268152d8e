import { useCallback, useState, useEffect } from "react";
import { useSocket } from "./context/SocketProvider";
import { useNavigate } from "react-router";

function App() {
  const [email, setEmail] = useState("");
  const [room, setRoom] = useState("");
  const socket = useSocket();
  const navigate = useNavigate();

  // console.log(socket);

  const handleSubmit = useCallback(
    (e) => {
      e.preventDefault();
      socket.emit("room:join", { email, room });
    },
    [email, room, socket]
  );

  const handleJoinRoom = useCallback((data) => {
    const { email, room } = data;
    navigate(`/room/${room}`);
  }, [navigate]);

  useEffect(() => {
    socket.on("room:join", handleJoinRoom);
    return () => {
      socket.off("room:join", handleJoinRoom);
    };
  }, [socket, handleJoinRoom]);

  return (
    <div className='max-w-md mx-auto bg-white p-6 rounded shadow'>
      <h2 className='text-lg font-bold mb-4'>Video Call Lobby</h2>
      <form onSubmit={handleSubmit}>
        <label className='block mb-2'>
          <span className='text-gray-700'>Email ID</span>
          <input
            type='email'
            placeholder='Enter your email'
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className='block w-full px-4 py-3 text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none'
          />
        </label>
        <label className='block mb-2'>
          <span className='text-gray-700'>Room Code</span>
          <input
            type='text'
            placeholder='Enter your room code'
            value={room}
            onChange={(e) => setRoom(e.target.value)}
            className='block w-full px-4 py-3 text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none'
          />
        </label>
        <button
          type='submit'
          className='inline-block px-6 py-2.5 bg-orange-500 text-white font-medium text-xs leading-tight uppercase rounded shadow-md hover:bg-orange-600 focus:bg-orange-600 focus:shadow-md focus:ring-0 active:bg-orange-700 transition duration-150 ease-in-out'
        >
          Join
        </button>
      </form>
    </div>
  );
}

export default App;
