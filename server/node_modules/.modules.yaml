hoistPattern:
  - '*'
hoistedDependencies:
  '@socket.io/component-emitter@3.1.2':
    '@socket.io/component-emitter': private
  '@types/cors@2.8.18':
    '@types/cors': private
  '@types/node@22.15.29':
    '@types/node': private
  accepts@1.3.8:
    accepts: private
  anymatch@3.1.3:
    anymatch: private
  balanced-match@1.0.2:
    balanced-match: private
  base64id@2.0.0:
    base64id: private
  binary-extensions@2.3.0:
    binary-extensions: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  chokidar@3.6.0:
    chokidar: private
  concat-map@0.0.1:
    concat-map: private
  cookie@0.7.2:
    cookie: private
  cors@2.8.5:
    cors: private
  debug@4.3.7(supports-color@5.5.0):
    debug: private
  engine.io-parser@5.2.3:
    engine.io-parser: private
  engine.io@6.6.4:
    engine.io: private
  fill-range@7.1.1:
    fill-range: private
  fsevents@2.3.3:
    fsevents: private
  glob-parent@5.1.2:
    glob-parent: private
  has-flag@3.0.0:
    has-flag: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  negotiator@0.6.3:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  picomatch@2.3.1:
    picomatch: private
  pstree.remy@1.1.8:
    pstree.remy: private
  readdirp@3.6.0:
    readdirp: private
  semver@7.7.2:
    semver: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  socket.io-adapter@2.5.5:
    socket.io-adapter: private
  socket.io-parser@4.2.4:
    socket.io-parser: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  touch@3.1.1:
    touch: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  vary@1.1.2:
    vary: private
  ws@8.17.1:
    ws: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sat, 31 May 2025 17:28:37 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
